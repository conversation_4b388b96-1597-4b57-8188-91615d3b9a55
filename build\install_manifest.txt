/home/<USER>/korea_3/install/_setup_util.py
/home/<USER>/korea_3/install/env.sh
/home/<USER>/korea_3/install/setup.bash
/home/<USER>/korea_3/install/local_setup.bash
/home/<USER>/korea_3/install/setup.sh
/home/<USER>/korea_3/install/local_setup.sh
/home/<USER>/korea_3/install/setup.zsh
/home/<USER>/korea_3/install/local_setup.zsh
/home/<USER>/korea_3/install/.rosinstall
/home/<USER>/korea_3/install/share/imu_release/cmake/imu_release-msg-paths.cmake
/home/<USER>/korea_3/install/share/roseus/ros/imu_release/manifest.l
/home/<USER>/korea_3/install/lib/python2.7/dist-packages/imu_release/__init__.pyc
/home/<USER>/korea_3/install/lib/python2.7/dist-packages/imu_release/__init__.py
/home/<USER>/korea_3/install/lib/pkgconfig/imu_release.pc
/home/<USER>/korea_3/install/share/imu_release/cmake/imu_release-msg-extras.cmake
/home/<USER>/korea_3/install/share/imu_release/cmake/imu_releaseConfig.cmake
/home/<USER>/korea_3/install/share/imu_release/cmake/imu_releaseConfig-version.cmake
/home/<USER>/korea_3/install/share/imu_release/package.xml
/home/<USER>/korea_3/install/lib/imu_release/imudriver
/home/<USER>/korea_3/install/share/imu_release/launch/imu.launch
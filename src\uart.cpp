

#include "USB.h"

namespace usb {
    USB::USB() {
        fdSerial = 0;
        usbParamIn = {};
    }

    USB::~USB() {

    }

    int USB::initUSB() {
        int iSetOpt = 0;//SetOpt 的增量i

        //openPort
        if ((fdSerial = this->openPort(fdSerial)) < 0) {
            perror("open_port error");
            return -1;
        }

        if ((iSetOpt = this->setOpt(fdSerial, usbParamIn.baudRate, 8, 'N', 1)) < 0) {
            perror("set_opt error");
            return -1;
        }
        //printf("Serial fdSerial = %d\n", fdSerial);

        tcflush(fdSerial, TCIOFLUSH);//清掉串口缓存
        fcntl(fdSerial, F_SETFL, 0);
        return 1;
    }

    int USB::readDataTty(std::vector<unsigned char> &rcv_buf, int TimeOut) {
        unsigned char rcv_buf1[1024 * 8] = {0};
        int len = 1024 * 8;
        int recLen = this->readDataTty(rcv_buf1, TimeOut, len);
        rcv_buf.resize(recLen, 0);
        for (int i = 0; i < recLen; ++i) {
            rcv_buf[i] = rcv_buf1[i];
        }
        return recLen;
    }

    int USB::readDataTty(unsigned char *rcv_buf, int TimeOut, int Len) {
        return this->readDataTty(fdSerial, rcv_buf, TimeOut, Len);
    }

    int USB::readDataTty(int fd, unsigned char *rcv_buf, int TimeOut, int Len) {
        int retval;
        fd_set rfds;
        struct timeval tv{};
        int ret, pos;
        tv.tv_sec = TimeOut / 1000;  //set the rcv wait time
        tv.tv_usec = TimeOut % 1000 * 1000;  //100000us = 0.1s

        pos = 0;
        while (1) {
            FD_ZERO(&rfds);
            FD_SET(fd, &rfds);
            retval = select(fd + 1, &rfds, nullptr, nullptr, &tv);
            if (retval == -1) {
                perror("select()");
                break;
            } else if (retval) {
                ret = read(fd, rcv_buf + pos, 1);
                //std::cout<<"ret = "<<ret<<std::endl;
                if (-1 == ret) {
                    break;
                }
                pos++;
                if (Len <= pos) {
                    break;
                }
            } else {
                break;
            }
        }
        return pos;
    }

    int USB::sendDataTty(std::vector<unsigned char> send_buf) {
        unsigned char send_buf1[1024 * 8] = {0};
        int len = (int) send_buf.size();
        for (int i = 0; i < len; ++i) {
            send_buf1[i] = send_buf[i];
        }
        return this->sendDataTty(send_buf1, len);
    }

    int USB::sendDataTty(unsigned char *send_buf, int Len) {
        return this->sendDataTty(fdSerial, send_buf, Len);
    }

    int USB::sendDataTty(int fd, unsigned char *send_buf, int Len) {
        ssize_t ret;

        ret = write(fd, send_buf, Len);
        if (ret == -1) {
            printf("write device error!!!\n");
            return -1;
        }

        return 1;
    }

    int USB::openPort(int fd) {
        std::string ttyUSB = usbParamIn.usbType;
        fd = open(ttyUSB.c_str(), O_RDWR | O_NOCTTY | O_NDELAY);
        if (-1 == fd) {
            perror("Can't Open Serial Port.");
            std::cout << "Open " << ttyUSB << " failed." << std::endl;
            return (-1);
        } else {
            std::cout << "Open " << ttyUSB << " success." << std::endl;
        }

        if (fcntl(fd, F_SETFL, 0) < 0) {
            //printf("fcntl failed!\n");
        } else {
            //printf("fcntl=%d\n", fcntl(fd, F_SETFL, 0));
        }
        if (isatty(STDIN_FILENO) == 0) {
            //printf("standard input is not a terminal device\n");
        } else {
            //printf("is a tty success!\n");
        }
        //printf("fd-open=%d\n", fd);
        return fd;
    }

    int USB::setOpt(int fd, int nSpeed, int nBits, char nEvent, int nStop) {
        struct termios newtio, oldtio;
        if (tcgetattr(fd, &oldtio) != 0) {
            perror("SetupSerial 1");
            return -1;
        }
        bzero(&newtio, sizeof(newtio));
        newtio.c_cflag |= CLOCAL | CREAD;
        newtio.c_cflag &= ~CSIZE;

        switch (nBits) {
            case 7:
                newtio.c_cflag |= CS7;
                break;
            case 8:
                newtio.c_cflag |= CS8;
                break;
        }

        switch (nEvent) {
            case 'O':                     //奇校验
                newtio.c_cflag |= PARENB;
                newtio.c_cflag |= PARODD;
                newtio.c_iflag |= (INPCK | ISTRIP);
                break;
            case 'E':                     //偶校验
                newtio.c_iflag |= (INPCK | ISTRIP);
                newtio.c_cflag |= PARENB;
                newtio.c_cflag &= ~PARODD;
                break;
            case 'N':                    //无校验
                newtio.c_cflag &= ~PARENB;
                break;
        }

        switch (nSpeed) {
            case 2400:
                cfsetispeed(&newtio, B2400);
                cfsetospeed(&newtio, B2400);
                break;
            case 4800:
                cfsetispeed(&newtio, B4800);
                cfsetospeed(&newtio, B4800);
                break;
            case 9600:
                cfsetispeed(&newtio, B9600);
                cfsetospeed(&newtio, B9600);
                break;
            case 19200:
                cfsetispeed(&newtio, B19200);
                cfsetospeed(&newtio, B19200);
                break;
            case 38400:
                cfsetispeed(&newtio, B38400);
                cfsetospeed(&newtio, B38400);
                break;
            case 57600:
                cfsetispeed(&newtio, B57600);
                cfsetospeed(&newtio, B57600);
                break;
            case 115200:
                cfsetispeed(&newtio, B115200);
                cfsetospeed(&newtio, B115200);
                break;
            case 230400:
                cfsetispeed(&newtio, B230400);
                cfsetospeed(&newtio, B230400);
                break;
            case 460800:
                cfsetispeed(&newtio, B460800);
                cfsetospeed(&newtio, B460800);
                break;
            /*case 750000:
                cfsetispeed(&newtio, B750000);
                cfsetospeed(&newtio, B750000);
                break;*/
            case 2000000:
                cfsetispeed(&newtio, B2000000);
                cfsetospeed(&newtio, B2000000);
                break;
            default:
                cfsetispeed(&newtio, B9600);
                cfsetospeed(&newtio, B9600);
                break;
        }
        if (nStop == 1) {
            newtio.c_cflag &= ~CSTOPB;
        } else if (nStop == 2) {
            newtio.c_cflag |= CSTOPB;
        }
        newtio.c_cc[VTIME] = 0;
        newtio.c_cc[VMIN] = 0;
        tcflush(fd, TCIFLUSH);
        if ((tcsetattr(fd, TCSANOW, &newtio)) != 0) {
            perror("com set error");
            return -1;
        }
        //printf("set done!\n");
        return 0;
    }

    int USB::closeTty() {
        close(fdSerial);
        return 1;
    }

}

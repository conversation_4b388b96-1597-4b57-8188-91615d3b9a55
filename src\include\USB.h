#pragma once

#include <cstdio>
#include <cstring>
#include <iostream>
#include <vector>
#include <fcntl.h>
#include <unistd.h>
#include <termios.h> //set baud rate
#include <sys/select.h>

#include <sys/time.h>
#include <sys/types.h>
#include <cerrno>
#include <sys/stat.h>
#include <cstdlib>

namespace usb {
    struct USBParamIn {
        std::string usbType;
        int baudRate;

        USBParamIn() {
            usbType = "/dev/ttyUSB0";
            baudRate = 115200;
        }

        USBParamIn(std::string _usbType, int _baudRate) {
            usbType = _usbType;
            baudRate = _baudRate;
        }
    };

    class USB {
    public:
        USB();

        ~USB();

    public:
        int fdSerial;
        USBParamIn usbParamIn;
    public:
        int initUSB();

        int readDataTty(std::vector<unsigned char> &rcv_buf, int TimeOut);

        int readDataTty(unsigned char *rcv_buf, int TimeOut, int Len);

        int readDataTty(int fd, unsigned char *rcv_buf, int TimeOut, int Len);

        int sendDataTty(std::vector<unsigned char> send_buf);

        int sendDataTty(unsigned char *send_buf, int Len);

        int sendDataTty(int fd, unsigned char *send_buf, int Len);

        int openPort(int fd);

        int setOpt(int fd, int nSpeed, int nBits, char nEvent, int nStop);

        int closeTty();
    };
}




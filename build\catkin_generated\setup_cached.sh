#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/home/<USER>/korea_3/devel:$CMAKE_PREFIX_PATH"
export PATH='/opt/ros/kinetic/bin:/home/<USER>/anaconda3/bin:/home/<USER>/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/usr/local/cuda-10.0/bin'
export PWD='/home/<USER>/korea_3/build'
export ROSLISP_PACKAGE_DIRECTORIES='/home/<USER>/korea_3/devel/share/common-lisp'
export ROS_PACKAGE_PATH="/home/<USER>/korea_3/src:$ROS_PACKAGE_PATH"
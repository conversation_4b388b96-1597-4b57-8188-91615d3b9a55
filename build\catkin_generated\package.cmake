set(_CATKIN_CURRENT_PACKAGE "imu_release")
set(imu_release_VERSION "0.0.0")
set(imu_release_MAINTAINER "tian<PERSON><PERSON> <<EMAIL>>")
set(imu_release_PACKAGE_FORMAT "2")
set(imu_release_BUILD_DEPENDS "roscpp" "rospy" "std_msgs" "geometry_msgs" "message_generation" "sensor_msgs")
set(imu_release_BUILD_EXPORT_DEPENDS "roscpp" "rospy" "std_msgs" "sensor_msgs")
set(imu_release_BUILDTOOL_DEPENDS "catkin")
set(imu_release_BUILDTOOL_EXPORT_DEPENDS )
set(imu_release_EXEC_DEPENDS "roscpp" "rospy" "std_msgs" "message_runtime" "geometry_msgs" "sensor_msgs")
set(imu_release_RUN_DEPENDS "roscpp" "rospy" "std_msgs" "message_runtime" "geometry_msgs" "sensor_msgs")
set(imu_release_TEST_DEPENDS )
set(imu_release_DOC_DEPENDS )
set(imu_release_URL_WEBSITE "")
set(imu_release_URL_BUGTRACKER "")
set(imu_release_URL_REPOSITORY "")
set(imu_release_DEPRECATED "")
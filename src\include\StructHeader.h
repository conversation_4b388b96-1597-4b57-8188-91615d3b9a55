//
// Created by niu<PERSON><PERSON> on 2024/3/6.
//

#pragma once

#include <iostream>
#include <vector>

namespace ins {
    typedef struct poll_data_F {
        uint16_t data1;
        uint16_t data2;
        uint16_t data3;
    } POLL_DATA_F;

#pragma pack(push, 1)
    /*
       该指令的目的是告诉编译器按照 1 字节对齐，
       这样可以确保结构体不会被填充额外的字节，
       从而强制使得 DATA_STREAM 保留原有的字节长度，即 65个字节。
    */
    typedef struct {
        //帧头:index 0~2
        uint8_t header[3];
        //欧拉角
        int16_t roll;//横滚角:index 3~4
        int16_t pitch;//俯仰角:index 5~6
        int16_t yaw;//航向角:index 7~8
        //陀螺仪
        int16_t gyro_X;//陀螺x轴: index 9~10
        int16_t gyro_Y;//陀螺y轴: index 11~12
        int32_t gyro_Z;//陀螺z轴: index 13~16
        //加速度
        int16_t accel_X;//x轴加速度: index 17~18
        int16_t accel_Y;//y轴加速度: index 19~20
        int16_t accel_Z;//z轴加速度: index 21~22
        //纬度、经度、海拔
        int32_t latitude;//纬度: index 23~26
        int32_t longitude;//经度: index 27~30
        int32_t altitude;//海拔: index 31~34
        //东北天方向速度
        int16_t east_Vel;//东向速度: index 35~36
        int16_t north_Vel;//北向速度: index 37~38
        int16_t celestial_Vel;//天向速度: index 39~40
        //状态【bit0:位置 bit1:速度 bit2:姿态 bit3:航向角】
        uint8_t status;//状态: index 41
        //保留位
        uint8_t reserved[6];//保留位: index 42~47
        //轮询数据
        POLL_DATA_F poll_Frame;//轮询数据: index 48~53
        //GPS时间，周内毫秒
        uint32_t gps_Time;//GPS时间: index 54~57
        //轮询数据类型
        uint8_t type;//轮询数据类型: index 58
        //异或校验index为:0~58 的字节
        uint8_t xor_Verify_058;//异或校验: index 59
        //GPS周
        uint32_t gps_Week;//GPS周: index 60~63
        //异或校验index为:0~63 的字节
        uint8_t xor_Verify_063;//异或校验: index 64

    } DATA_STREAM;
#pragma pack(pop)

    typedef union {
        DATA_STREAM data;
        uint8_t buf[sizeof(DATA_STREAM)];
    } FRAME;

    // GPS状态
    struct GPSStatus {
        int posStatus;// 定位状态
        int satellites;// 卫星数
        int directionalStatus;// 定向状态
    };

    struct INSData {
        int week; // 自1980-1-6至当前的星期数
        double second;  // 周内秒，本周00：00：00至当前的秒数
        double timestamp;// 时间戳 秒
        std::string gpsDate;// GPS时间戳转换为日期
        double localTimestamp;// 本地电脑时间戳 秒
        std::string localDate;// 本地电脑日期
        double yaw;        // 偏航角
        double pitch;          // 俯仰角
        double roll;           // 滚转角
        double gyro_X;// 陀螺仪参数（单位:deg）
        double gyro_Y;
        double gyro_Z;
        double accel_X;// 加速度(单位:g)
        double accel_Y;
        double accel_Z;
        double latitude;      // 纬度 deg
        double longitude;     // 经度 deg
        double altitude;       // 高度 m
        double vE;             // 东 速度 m/s
        double vN;             // 北 速度 m/s
        double vU;             // 天 速度 m/s
        int status;          // 系统状态
        POLL_DATA_F pollDataF;// 轮询数据
        int type;// 轮询数据类型
        double lasStd;
        double lonStd;
        double hStd;
        double temperature;// 温度
        GPSStatus gpsStatus;// GPS状态
        double wheelSpeed;// 轮速
        double baseLine;// 基线长度 m
        int rtkStatus;// RTK状态
        int carCalibrationStatus;// 跑车标定状态
        double gpsX;// GPS经纬度转为平面坐标
        double gpsY;// GPS经纬度转为平面坐标

        /*void init() {
            week = 0;
            second = 0;
            timestamp = 0;
            yaw = 0;
            pitch = 0;
            roll = 0;
            gyro_X = 0;
            gyro_Y = 0;
            gyro_Z = 0;
            accel_X = 0;
            accel_Y = 0;
            accel_Z = 0;
            latitude = 0;
            longitude = 0;
            altitude = 0;
            vE = 0;
            vN = 0;
            vU = 0;
            status = 0;
            pollDataF = {};
            type = 0;
        }

        INSData() {
            this->init();
        }

        INSData(const INSData &_param) {
            this->init();
            week = _param.week;
            second = _param.second;
            timestamp = _param.timestamp;
            yaw = _param.yaw;
            pitch = _param.pitch;
            roll = _param.roll;
            gyro_X = _param.gyro_X;
            gyro_Y = _param.gyro_Y;
            gyro_Z = _param.gyro_Z;
            accel_X = _param.accel_X;
            accel_Y = _param.accel_Y;
            accel_Z = _param.accel_Z;
            latitude = _param.latitude;
            longitude = _param.longitude;
            altitude = _param.altitude;
            vE = _param.vE;
            vN = _param.vN;
            vU = _param.vU;
            status = _param.status;
            pollDataF = _param.pollDataF;
            type = _param.type;
        }*/
    };

    struct SetInsParams {
        //第1个字节为开关，后面字节为数据
        u_char baudRateType[2]{};// 波特率类型，0x00~0xFE
        int hz[2]{};//设置(数据输出)频率
        float gnssArmLen[4]{};//设置GNSS杆臂参数
        float antennaInstallAngle[4]{};//设置天线安装角度
        float gnssPosOffset[4]{};//惯导-后轮轴中心位置矢量
        float gnssAngleOffset[4]{};//惯导角度安装偏差
        u_char coordinateType[2]{};//设置用户坐标轴类型，1~24
        float baseLineLen[2]{};//设置GPS天线基线长度
        bool getDeviceTypeFlag = true;//获取设备型号
        bool curingParamsFlag = true;//固化参数
        bool restoreFactorySettingsFlag = false;//恢复出厂设置
        bool setAllParamsFlag = false;//设置所有参数（全部应用）
        bool paramsReadBackFlag = false;//参数回读
        bool readBootVersionFlag = true;//读取固件版本
    };

    struct ParamIn {
        std::ofstream *fLog{};
        std::string serialPortPath;//串口路径
        int baudRate = 0;//波特率
        std::string dataSavePath;//数据保存路径
        int syncLocalTime = 0;//同步GPS时间到本地：1--同步，0--不同步
        SetInsParams setInsParams{};
    };

    struct ParamOut {
        std::vector<INSData> insDatas;

        /*void init() {
            insDatas.clear();
        }

        ParamOut() {
            this->init();
        }

        ParamOut(const ParamOut &_param) {
            this->init();
            //insDatas = _param.insDatas;
            for (auto insData: _param.insDatas) {
                insDatas.push_back(insData);
            }
        }*/
    };

    union uint2byte {
        unsigned int value;
        u_char byte[4];
    };

    union double2byte {
        double value;
        u_char byte[8];
    };

    union float2byte {
        float value;
        u_char byte[4];
    };

    union ushort2byte {
        unsigned short value;
        u_char byte[2];
    };

    union uint642byte {
        unsigned long int value;
        u_char byte[8];
    };
}

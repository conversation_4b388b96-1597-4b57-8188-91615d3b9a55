#include "imu.h"
#include "ros/ros.h"
//#include <imu_release/ins622.h>
#include "sensor_msgs/Imu.h"
#include "sensor_msgs/NavSatFix.h"
#include "tf/LinearMath/Matrix3x3.h"
#include "geometry_msgs/Quaternion.h"
#include "tf/transform_datatypes.h"
#include "signal.h"
#include <cmath>

#define PI 3.1415926

#define GPS_10HZ 20

#define GPS_5HZ  2

uint16_t GpsRate=GPS_5HZ; //这里改GNSS话题频率，要配合硬件修改

ins::INSData lastedInsData{};
static uint32_t DetectUsbCount = 0;

bool RunFlag=1;

void thr_DetectUsb(void);
void thr_TestCount(void);

namespace ins {

    INS::INS()
    {

    }

    INS::~INS()
    {

    }

    int INS::init() {
        ros::NodeHandle pnh;
         ros::NodeHandle private_pnh("~");
        std::string usb_port;
        int serial_rate;

        private_pnh.param<std::string>("UsbPort",usb_port,"/dev/ttyS0");
        printf("\n usb_port = %s",usb_port.c_str());
        usbBody.usbParamIn.usbType = usb_port.c_str();//paramIn.serialPortPath;
        printf("\n usbBody.usbParamIn.usbType = %s",usb_port.c_str());


        private_pnh.param("BaudRate", serial_rate,9600);
        printf("\n serial_rate = %d \n",serial_rate);
        usbBody.usbParamIn.baudRate = serial_rate;
        printf("\n usbBody.usbParamIn.baudRate = %d \n",usbBody.usbParamIn.baudRate);


        RunFlag = false;

        if (usbBody.initUSB() < 0) {
            //cout << "USB connect failed: " << paramIn.serialPortPath << " , " << paramIn.baudRate << endl;

            state = false;
            return -1;
        }

        state = true;

        RunFlag = true;
        std::thread receive_thread([this] { receiveThread(); });
        receive_thread.detach();
        std::thread t(&thr_DetectUsb);
        t.detach();
        return 1;

    }

    u_char INS::xorCheck(std::vector<u_char> buff) {
        u_char xorResult = 0;
        for (unsigned char i: buff) {
            xorResult ^= i;
        }
        return xorResult;
    }

    void INS::receiveThread() {
        vector<vector<u_char>> validFrameBuff;
        vector<vector<u_char>> validOtherFrameBuff;
        vector<u_char> invalidFrameBuff;
        vector<u_char> allBuff;
    
        FRAME oneFrame{};
        int frameLen = sizeof(oneFrame.buf);
        paramOutInside = {};
    
        temperature = 0;// 温度
        gpsStatus = {};// GPS状态
        wheelSpeed = 0;// 轮速
        baseLine = 0;// 基线长度 m
        rtkStatus = 0;// RTK状态
        carCalibrationStatus = 0;// 跑车标定状态
        int lostDataCount = 0; // 统计丢失数据次数
        const int MAX_LOST_COUNT = 10; // 设置最大允许丢失的次数
    
        while (state) {
            int recLen = 0;
            u_char buffRcvData[1024 * 8] = {0};
    
            recLen = usbBody.readDataTty(buffRcvData, 1, 1024 * 8);
            //double st = Util::timeMs();
            for (int i = 0; i < recLen; i++) {
                allBuff.push_back(buffRcvData[i]);
            }

            /*cout << "allBuff : ";
            for (int i = 0; i < allBuff.size(); i++) {
                cout << hex << (int) allBuff[i] << " ";
            }
            cout << endl;*/

            int index = 0;
            size_t allBuffSize = allBuff.size();
            while (index < allBuffSize) {
                // 数据标志头
                if (allBuff[index] == 0xBD && allBuff[index + 1] == 0xDB && allBuff[index + 2] == 0x0B) {
                    if (allBuffSize - index < frameLen) {
                        break;
                    }

                    vector<u_char> frameBuff(frameLen, 0);
                    size_t frameBuffSize = frameBuff.size();
                    for (size_t i = 0; i < frameBuffSize; ++i) {
                        frameBuff[i] = allBuff[index + i];
                    }

                    vector<u_char> crcBuff;
                    crcBuff.insert(crcBuff.begin(), frameBuff.begin(), frameBuff.end() - 6);
                    u_char crc = this->xorCheck(crcBuff);
                    if (crc == frameBuff.at(frameBuff.size() - 6)) {
                        validFrameBuff.push_back(frameBuff);
                    }
                    index += frameLen;
                } else if (allBuff[index] == 0xFA && allBuff[index + 1] == 0x55 && allBuff[index + 2] == 0xAA) {
                    // 其他信息标志头
                    int startInd = index;
                    vector<u_char> frameBuff;
                    /*frameBuff.push_back(allBuff[index]);
                    frameBuff.push_back(allBuff[index + 1]);   
                    frameBuff.push_back(allBuff[index + 2]);
                    index += 3;*/
                    bool oneFrameOk = false;
                    while (index < allBuffSize) {
                        frameBuff.push_back(allBuff[index]);
                        if (allBuff[index] == 0x00 && allBuff[index + 1] == 0xFF) {
                            oneFrameOk = true;
                            frameBuff.push_back(allBuff[index + 1]);
                            index += 2;
                            break;
                        } else {
                            index++;
                        }
                    }
                    if (oneFrameOk) {
                        validOtherFrameBuff.push_back(frameBuff);
                    } else {
                        index = startInd;
                        break;
                    }
                 
				}	else {
                    index++;
                }
            }
            allBuff.erase(allBuff.begin(), allBuff.begin() + index);
            /*cout << "allBuff1 : ";
            for (int i = 0; i < allBuff.size(); i++) {
                cout << hex << (int) allBuff[i] << " ";
            }
            cout << endl;*/
    
            this->analysisData(validFrameBuff);
           // this->analysisOtherData(validOtherFrameBuff);

            if (!validFrameBuff.empty()) {
                //cout << Util::timeMs() - st << endl;
            }
            validFrameBuff.clear();
         //   validOtherFrameBuff.clear();

            if (dataUsed && !paramOutInside.insDatas.empty()) {
                paramOut = paramOutInside;
                paramOutInside = {};
                dataUsed = false;
            }
        }
        usbBody.closeTty();
    }

    void INS::analysisData(vector<vector<u_char>> _validFrameBuff) {
        if (_validFrameBuff.empty()){
            return;
        }

          DetectUsbCount =0;

        for (int i = 0; i < _validFrameBuff.size(); ++i) {
            vector<u_char> frameBuff = _validFrameBuff.at(i);
            FRAME oneFrame = {};
            for (int j = 0; j < frameBuff.size(); ++j) {
                oneFrame.buf[j] = frameBuff.at(j);
            }
            INSData insData{};
            //4、解析欧拉角（单位:deg）
            insData.roll = (double) oneFrame.data.roll * 360.0 / 32768.0;
            insData.pitch = (double) oneFrame.data.pitch * 360.0 / 32768.0;
            insData.yaw = (double) oneFrame.data.yaw * 360.0 / 32768.0;
            //5、解析陀螺仪参数（单位:deg）
            //hanyong:300 not 360
            insData.gyro_X = (double) oneFrame.data.gyro_X * 300.0 / 32768.0;
            insData.gyro_Y = (double) oneFrame.data.gyro_Y * 300.0 / 32768.0;
            insData.gyro_Z = (double) oneFrame.data.gyro_Z * 600.0 / 2147483648.0;
            //6、加速度(单位:g)
            insData.accel_X = (double) oneFrame.data.accel_X * 12.0 / 32768.0;
            insData.accel_Y = (double) oneFrame.data.accel_Y * 12.0 / 32768.0;
            insData.accel_Z = (double) oneFrame.data.accel_Z * 12.0 / 32768.0;
            //7、纬度(单位:deg)、经度(单位:deg)、海拔(单位:米)
            insData.latitude = (double) oneFrame.data.latitude * 1.00e-07;
            insData.longitude = (double) oneFrame.data.longitude * 1.00e-07;
            insData.altitude = (double) oneFrame.data.altitude * 1.00e-03;
            //8、东北天方向速度（m/s）
            insData.vE = (double) oneFrame.data.east_Vel * 100.0 / 32768.0;
            insData.vN = (double) oneFrame.data.north_Vel * 100.0 / 32768.0;
            insData.vU = (double) oneFrame.data.celestial_Vel * 100.0 / 32768.0;
            //9、状态
            insData.status = oneFrame.data.status;
            //10、保留位(本例子不再做处理)
            //11、轮询数据
            POLL_DATA_F poll_Frame_;
            poll_Frame_.data1 = oneFrame.data.poll_Frame.data1;
            poll_Frame_.data2 = oneFrame.data.poll_Frame.data2;
            poll_Frame_.data3 = oneFrame.data.poll_Frame.data3;
            //12、GPS时间(单位:ms)，周内毫秒
            double gps_Time = (double) oneFrame.data.gps_Time * 0.25;
            insData.second = gps_Time / 1000.0;
            //13、轮询数据类型
            insData.type = oneFrame.data.type;
            //14、异或校验index为:0~58 的字节（本例子不再做处理）
            //15、GPS周
            insData.week = oneFrame.data.gps_Week;
            //16、异或校验index为:0~63 的字节（本例子不再做处理）

            // 协方差
            if (insData.type == 0) {
                lasStd = poll_Frame_.data1;
                lonStd = poll_Frame_.data2;
                hStd = poll_Frame_.data3;
            }
            insData.lasStd = lasStd;
            insData.lonStd = lonStd;
            insData.hStd = hStd;
            // 设备温度
            if (insData.type == 22) {
                temperature = poll_Frame_.data1 * 200.0 / 32768.0;
            }
            insData.temperature = temperature;
            // GPS状态
            if (insData.type == 32) {
                gpsStatus.posStatus = poll_Frame_.data1;
                gpsStatus.satellites = poll_Frame_.data2;
                gpsStatus.directionalStatus = poll_Frame_.data3;
            }
            insData.gpsStatus = gpsStatus;
            // 轮速
            if (insData.type == 33) {
                wheelSpeed = poll_Frame_.data2;
            }
            insData.wheelSpeed = wheelSpeed;
            // 基线长度 m
            if (insData.type == 34) {
                baseLine = poll_Frame_.data1 / 1000.0;
            }
            insData.baseLine = baseLine;
            // RTK状态
            if (insData.type == 35) {
                rtkStatus = poll_Frame_.data1;
            }
            insData.rtkStatus = rtkStatus;
            // 跑车标定状态
            if (insData.type == 36) {
                carCalibrationStatus = poll_Frame_.data1;
            }
            insData.carCalibrationStatus = carCalibrationStatus;

            lastedInsData = insData;
            paramOutInside.insDatas.push_back(insData);


        }
		
		}
}

static void SigHandler(int sig)
{
    if (sig == SIGINT)
    {
		RunFlag=0;
    }
}
void thr_DetectUsb(void)
{

    while(1)
    {

        DetectUsbCount ++;

        if(DetectUsbCount > 1000)
        {
            DetectUsbCount = 1000;

            RunFlag = 0;
        }

        usleep(5*1000);

    }
}
void thr_TestCount(void)
{

    while(1)
    {

        DetectUsbCount = 0;



        usleep(1000*1000);
    }
}
int main(int argc, char** argv)
{

    ros::init(argc, argv, "imu_node");

    ins::INS ImuHandler;

    ImuHandler.init();


    ros::NodeHandle n;
    ros::Publisher  imu_pub = n.advertise<sensor_msgs::Imu>("imu",200);//200HZ
    ros::Publisher  gps_pub = n.advertise<sensor_msgs::NavSatFix>("gps",200);//200HZ

    ros::Rate r(200);//HZ

    ros::Time current_time, last_time;
	current_time = ros::Time::now();
	printf("\n current_time = %d \n",current_time.sec);
	last_time = ros::Time::now();

    uint16_t GpsCount = 0;

    while(n.ok() &&  RunFlag)
    {

        current_time = ros::Time::now();

        double dt = (current_time - last_time).toSec();
	   // std::cout << "dt:" << dt << std::endl;


        last_time = current_time;



            sensor_msgs::NavSatFix gps_data;
            sensor_msgs::Imu imu_msg;

            imu_msg.header.stamp = ros::Time::now();
            imu_msg.header.frame_id = "imu_link";


            tf::Matrix3x3 rotation_matrix;
            rotation_matrix.setEulerYPR(lastedInsData.yaw/180 * PI, lastedInsData.pitch/180 * PI, lastedInsData.roll/180 * PI);
            tf::Quaternion quat;
            rotation_matrix.getRotation( quat );
            imu_msg.orientation.x = quat.getX();
            imu_msg.orientation.y = quat.getY();
            imu_msg.orientation.z = quat.getZ();
            imu_msg.orientation.w = quat.getW();

            // imu_msg.angular_velocity.x = lastedInsData.gyro_X;
            // imu_msg.angular_velocity.y = lastedInsData.gyro_Y;
            // imu_msg.angular_velocity.z = lastedInsData.gyro_Z;

            imu_msg.angular_velocity.x = lastedInsData.gyro_X * PI / 180;
            imu_msg.angular_velocity.y = lastedInsData.gyro_Y * PI / 180;
            imu_msg.angular_velocity.z = lastedInsData.gyro_Z * PI / 180;


            // Fill linear acceleration data
            imu_msg.linear_acceleration.x = lastedInsData.accel_X * 9.78831;
            imu_msg.linear_acceleration.y =  lastedInsData.accel_Y * 9.78831;
            imu_msg.linear_acceleration.z =  lastedInsData.accel_Z * 9.78831;


            // Publish the messages
            imu_pub.publish(imu_msg);

            GpsCount ++;

            if(GpsCount >= GpsRate)
            {
               GpsCount = 0;

                gps_data.header.stamp = ros::Time::now();
                gps_data.header.frame_id = "gps_link";

                gps_data.latitude=lastedInsData.latitude;
                gps_data.longitude=lastedInsData.longitude;
                gps_data.altitude=lastedInsData.altitude;
                gps_data.status.status=lastedInsData.rtkStatus;
                gps_data.status.service=lastedInsData.gpsStatus.satellites;
                gps_data.position_covariance[0]=lastedInsData.lasStd;
                gps_data.position_covariance[4]=lastedInsData.lonStd;
                gps_data.position_covariance[8]=lastedInsData.hStd;

                gps_pub.publish(gps_data);     

            }

             ros::spinOnce();
             r.sleep();

    }


    return 0;
}


# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/korea_3/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/korea_3/build

# Include any dependencies generated for this target.
include gtest/gtest/CMakeFiles/gtest_main.dir/depend.make

# Include the progress variables for this target.
include gtest/gtest/CMakeFiles/gtest_main.dir/progress.make

# Include the compile flags for this target's objects.
include gtest/gtest/CMakeFiles/gtest_main.dir/flags.make

gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o: gtest/gtest/CMakeFiles/gtest_main.dir/flags.make
gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o: /usr/src/gtest/src/gtest_main.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/korea_3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o"
	cd /home/<USER>/korea_3/build/gtest/gtest && /usr/bin/c++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/gtest_main.dir/src/gtest_main.cc.o -c /usr/src/gtest/src/gtest_main.cc

gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gtest_main.dir/src/gtest_main.cc.i"
	cd /home/<USER>/korea_3/build/gtest/gtest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /usr/src/gtest/src/gtest_main.cc > CMakeFiles/gtest_main.dir/src/gtest_main.cc.i

gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gtest_main.dir/src/gtest_main.cc.s"
	cd /home/<USER>/korea_3/build/gtest/gtest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /usr/src/gtest/src/gtest_main.cc -o CMakeFiles/gtest_main.dir/src/gtest_main.cc.s

gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.requires:

.PHONY : gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.requires

gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.provides: gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.requires
	$(MAKE) -f gtest/gtest/CMakeFiles/gtest_main.dir/build.make gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.provides.build
.PHONY : gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.provides

gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.provides.build: gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o


# Object files for target gtest_main
gtest_main_OBJECTS = \
"CMakeFiles/gtest_main.dir/src/gtest_main.cc.o"

# External object files for target gtest_main
gtest_main_EXTERNAL_OBJECTS =

gtest/gtest/libgtest_main.so: gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o
gtest/gtest/libgtest_main.so: gtest/gtest/CMakeFiles/gtest_main.dir/build.make
gtest/gtest/libgtest_main.so: gtest/gtest/libgtest.so
gtest/gtest/libgtest_main.so: gtest/gtest/CMakeFiles/gtest_main.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/korea_3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared library libgtest_main.so"
	cd /home/<USER>/korea_3/build/gtest/gtest && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/gtest_main.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
gtest/gtest/CMakeFiles/gtest_main.dir/build: gtest/gtest/libgtest_main.so

.PHONY : gtest/gtest/CMakeFiles/gtest_main.dir/build

gtest/gtest/CMakeFiles/gtest_main.dir/requires: gtest/gtest/CMakeFiles/gtest_main.dir/src/gtest_main.cc.o.requires

.PHONY : gtest/gtest/CMakeFiles/gtest_main.dir/requires

gtest/gtest/CMakeFiles/gtest_main.dir/clean:
	cd /home/<USER>/korea_3/build/gtest/gtest && $(CMAKE_COMMAND) -P CMakeFiles/gtest_main.dir/cmake_clean.cmake
.PHONY : gtest/gtest/CMakeFiles/gtest_main.dir/clean

gtest/gtest/CMakeFiles/gtest_main.dir/depend:
	cd /home/<USER>/korea_3/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/korea_3/src /usr/src/gtest /home/<USER>/korea_3/build /home/<USER>/korea_3/build/gtest/gtest /home/<USER>/korea_3/build/gtest/gtest/CMakeFiles/gtest_main.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : gtest/gtest/CMakeFiles/gtest_main.dir/depend



#pragma once

#include <iostream>
#include <vector>
#include "StructHeader.h"
#include "USB.h"
#include <thread>
#include <fstream>

using namespace std;

namespace ins {

    class INS {
    public:
        INS();

        ~INS();

    public:
            usb::USB usbBody;
        int init();

    public:
        void receiveThread();//数据接收

    private:
        ParamIn paramIn;
        ParamOut paramOut;
        ParamOut paramOutInside;
        bool dataUsed;
        bool state;// 连接状态 false-未连接，true-已连接


        bool ReceiveSuccessFlag;


        double lasStd;
        double lonStd;
        double hStd;
        double temperature;// 温度
        GPSStatus gpsStatus;// GPS状态
        double wheelSpeed;// 轮速
        double baseLine;// 基线长度 m
        int rtkStatus;// RTK状态
        int carCalibrationStatus;// 跑车标定状态


    private:
        u_char xorCheck(std::vector<u_char> buff);


        void analysisData(vector<vector<u_char>> _validFrameBuff);

    };

} // ins

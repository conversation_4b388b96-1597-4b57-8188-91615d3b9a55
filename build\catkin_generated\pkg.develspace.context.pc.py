# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/korea_3/src/include".split(';') if "/home/<USER>/korea_3/src/include" != "" else []
PROJECT_CATKIN_DEPENDS = "geometry_msgs;roscpp;rospy;std_msgs;message_runtime".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "imu_release"
PROJECT_SPACE_DIR = "/home/<USER>/korea_3/devel"
PROJECT_VERSION = "0.0.0"

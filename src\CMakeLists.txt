cmake_minimum_required(VERSION 3.0.2)
SET(CMAKE_BUILD_TYPE "Release")
#SET(CMAKE_BUILD_TYPE "Debug")
project(imu_release)

add_compile_options(-std=c++11)

find_package(catkin REQUIRED COMPONENTS
  roscpp
  rospy
  std_msgs
  geometry_msgs
  message_generation
  message_filters
  visualization_msgs
  sensor_msgs
)

add_message_files(
  FILES
)

generate_messages(
               DEPENDENCIES
               std_msgs
)


catkin_package(
               INCLUDE_DIRS include
               CATKIN_DEPENDS geometry_msgs roscpp rospy std_msgs message_runtime
)

#——————————————————————————————————该指令的作用主要是指定要链接的动/静态库,库文件的路径————————————————————————————————————
link_directories(
					  /usr/local/lib 
)


#——————————————————————————————————相关头文件路径————————————————————————————————————
include_directories(
                   ${catkin_INCLUDE_DIRS}
                   ./include
                   usr/local/include     
)

#——————————————————————————————————可执行程序————————————————————————————————————
add_executable(imudriver
               ./main.cpp
               ./uart.cpp

)
add_dependencies(imudriver
 					 ${ins_pkg_EXPORTED_TARGETS}
	             ${catkin_EXPORTED_TARGETS}
)
target_link_libraries(imudriver
					      ${catkin_LIBRARIES}
)



install(TARGETS imudriver
    ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
    RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

install(DIRECTORY launch
    DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)

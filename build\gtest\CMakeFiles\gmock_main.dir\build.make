# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/korea_3/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/korea_3/build

# Include any dependencies generated for this target.
include gtest/CMakeFiles/gmock_main.dir/depend.make

# Include the progress variables for this target.
include gtest/CMakeFiles/gmock_main.dir/progress.make

# Include the compile flags for this target's objects.
include gtest/CMakeFiles/gmock_main.dir/flags.make

gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o: gtest/CMakeFiles/gmock_main.dir/flags.make
gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o: /usr/src/gtest/src/gtest-all.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/korea_3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o -c /usr/src/gtest/src/gtest-all.cc

gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.i"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /usr/src/gtest/src/gtest-all.cc > CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.i

gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.s"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /usr/src/gtest/src/gtest-all.cc -o CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.s

gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o.requires:

.PHONY : gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o.requires

gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o.provides: gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o.requires
	$(MAKE) -f gtest/CMakeFiles/gmock_main.dir/build.make gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o.provides.build
.PHONY : gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o.provides

gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o.provides.build: gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o


gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o: gtest/CMakeFiles/gmock_main.dir/flags.make
gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o: /usr/src/gmock/src/gmock-all.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/korea_3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/gmock_main.dir/src/gmock-all.cc.o -c /usr/src/gmock/src/gmock-all.cc

gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gmock_main.dir/src/gmock-all.cc.i"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /usr/src/gmock/src/gmock-all.cc > CMakeFiles/gmock_main.dir/src/gmock-all.cc.i

gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gmock_main.dir/src/gmock-all.cc.s"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /usr/src/gmock/src/gmock-all.cc -o CMakeFiles/gmock_main.dir/src/gmock-all.cc.s

gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o.requires:

.PHONY : gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o.requires

gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o.provides: gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o.requires
	$(MAKE) -f gtest/CMakeFiles/gmock_main.dir/build.make gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o.provides.build
.PHONY : gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o.provides

gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o.provides.build: gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o


gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o: gtest/CMakeFiles/gmock_main.dir/flags.make
gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o: /usr/src/gmock/src/gmock_main.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/korea_3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/gmock_main.dir/src/gmock_main.cc.o -c /usr/src/gmock/src/gmock_main.cc

gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/gmock_main.dir/src/gmock_main.cc.i"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /usr/src/gmock/src/gmock_main.cc > CMakeFiles/gmock_main.dir/src/gmock_main.cc.i

gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/gmock_main.dir/src/gmock_main.cc.s"
	cd /home/<USER>/korea_3/build/gtest && /usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /usr/src/gmock/src/gmock_main.cc -o CMakeFiles/gmock_main.dir/src/gmock_main.cc.s

gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.requires:

.PHONY : gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.requires

gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.provides: gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.requires
	$(MAKE) -f gtest/CMakeFiles/gmock_main.dir/build.make gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.provides.build
.PHONY : gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.provides

gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.provides.build: gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o


# Object files for target gmock_main
gmock_main_OBJECTS = \
"CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o" \
"CMakeFiles/gmock_main.dir/src/gmock-all.cc.o" \
"CMakeFiles/gmock_main.dir/src/gmock_main.cc.o"

# External object files for target gmock_main
gmock_main_EXTERNAL_OBJECTS =

gtest/libgmock_main.so: gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o
gtest/libgmock_main.so: gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o
gtest/libgmock_main.so: gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o
gtest/libgmock_main.so: gtest/CMakeFiles/gmock_main.dir/build.make
gtest/libgmock_main.so: gtest/CMakeFiles/gmock_main.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/korea_3/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX shared library libgmock_main.so"
	cd /home/<USER>/korea_3/build/gtest && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/gmock_main.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
gtest/CMakeFiles/gmock_main.dir/build: gtest/libgmock_main.so

.PHONY : gtest/CMakeFiles/gmock_main.dir/build

gtest/CMakeFiles/gmock_main.dir/requires: gtest/CMakeFiles/gmock_main.dir/usr/src/gtest/src/gtest-all.cc.o.requires
gtest/CMakeFiles/gmock_main.dir/requires: gtest/CMakeFiles/gmock_main.dir/src/gmock-all.cc.o.requires
gtest/CMakeFiles/gmock_main.dir/requires: gtest/CMakeFiles/gmock_main.dir/src/gmock_main.cc.o.requires

.PHONY : gtest/CMakeFiles/gmock_main.dir/requires

gtest/CMakeFiles/gmock_main.dir/clean:
	cd /home/<USER>/korea_3/build/gtest && $(CMAKE_COMMAND) -P CMakeFiles/gmock_main.dir/cmake_clean.cmake
.PHONY : gtest/CMakeFiles/gmock_main.dir/clean

gtest/CMakeFiles/gmock_main.dir/depend:
	cd /home/<USER>/korea_3/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/korea_3/src /usr/src/gmock /home/<USER>/korea_3/build /home/<USER>/korea_3/build/gtest /home/<USER>/korea_3/build/gtest/CMakeFiles/gmock_main.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : gtest/CMakeFiles/gmock_main.dir/depend

